#!/usr/bin/env python3
"""
Testerat Enhanced - Skill Gap Analyzer Testing
Live end-to-end testing of the Skill Gap Analyzer feature
"""

import asyncio
import time
from playwright.async_api import async_playwright, <PERSON>, Browser
import json
import logging
from datetime import datetime

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SkillGapAnalyzerTester:
    def __init__(self):
        self.base_url = "http://localhost:3000"
        self.test_credentials = {
            "email": "<EMAIL>",
            "password": "testpassword"
        }
        self.results = []
        
    async def run_comprehensive_test(self):
        """Run comprehensive end-to-end test of Skill Gap Analyzer"""
        logger.info("🚀 Starting Skill Gap Analyzer Comprehensive Test")
        
        async with async_playwright() as p:
            # Launch browser
            browser = await p.chromium.launch(headless=False, slow_mo=1000)
            context = await browser.new_context()
            page = await context.new_page()
            
            try:
                # Test sequence
                await self.test_authentication(page)
                await self.test_navigation_to_skill_gap_analyzer(page)
                await self.test_skill_assessment_form(page)
                await self.test_gap_analysis_workflow(page)
                await self.test_results_display(page)
                
                logger.info("✅ All tests completed successfully!")
                
            except Exception as e:
                logger.error(f"❌ Test failed: {str(e)}")
                await page.screenshot(path=f"error_screenshot_{int(time.time())}.png")
                
            finally:
                await browser.close()
                
        return self.results
    
    async def test_authentication(self, page: Page):
        """Test user authentication"""
        logger.info("🔐 Testing Authentication")
        
        try:
            # Navigate to login page
            await page.goto(f"{self.base_url}/login")
            await page.wait_for_load_state('networkidle')
            
            # Check if already logged in by looking for dashboard elements
            try:
                await page.wait_for_selector('[data-testid="user-menu"], .user-avatar, [href="/dashboard"]', timeout=3000)
                logger.info("✅ User already authenticated")
                self.results.append({"test": "authentication", "status": "success", "message": "Already authenticated"})
                return
            except:
                pass
            
            # Fill login form
            await page.fill('input[type="email"], input[name="email"]', self.test_credentials["email"])
            await page.fill('input[type="password"], input[name="password"]', self.test_credentials["password"])
            
            # Submit login
            await page.click('button[type="submit"], button:has-text("Sign In"), button:has-text("Login")')
            await page.wait_for_load_state('networkidle')
            
            # Verify login success
            await page.wait_for_selector('[data-testid="user-menu"], .user-avatar, [href="/dashboard"]', timeout=10000)
            
            logger.info("✅ Authentication successful")
            self.results.append({"test": "authentication", "status": "success", "message": "Login successful"})
            
        except Exception as e:
            logger.error(f"❌ Authentication failed: {str(e)}")
            self.results.append({"test": "authentication", "status": "failed", "error": str(e)})
            raise
    
    async def test_navigation_to_skill_gap_analyzer(self, page: Page):
        """Test navigation to Skill Gap Analyzer"""
        logger.info("🧭 Testing Navigation to Skill Gap Analyzer")
        
        try:
            # Try direct navigation first
            await page.goto(f"{self.base_url}/skills/gap-analyzer")
            await page.wait_for_load_state('networkidle')
            
            # Check if page loaded correctly
            await page.wait_for_selector('h1:has-text("Skill Gap Analyzer"), [data-testid="skill-gap-analyzer"]', timeout=10000)
            
            logger.info("✅ Successfully navigated to Skill Gap Analyzer")
            self.results.append({"test": "navigation", "status": "success", "message": "Direct navigation successful"})
            
        except Exception as e:
            logger.info("Direct navigation failed, trying through menu...")
            
            try:
                # Navigate through menu
                await page.goto(f"{self.base_url}/dashboard")
                await page.wait_for_load_state('networkidle')
                
                # Look for Tools menu or navigation
                tools_menu = page.locator('text="Tools", [data-testid="tools-menu"], .tools-dropdown')
                if await tools_menu.count() > 0:
                    await tools_menu.click()
                    await page.wait_for_timeout(1000)
                    
                    # Click on Skill Gap Analyzer
                    await page.click('text="Skill Gap Analyzer", [href="/skills/gap-analyzer"]')
                    await page.wait_for_load_state('networkidle')
                else:
                    # Try direct link in navigation
                    await page.click('[href="/skills/gap-analyzer"], text="Skill Gap Analyzer"')
                    await page.wait_for_load_state('networkidle')
                
                # Verify we're on the right page
                await page.wait_for_selector('h1:has-text("Skill Gap Analyzer"), [data-testid="skill-gap-analyzer"]', timeout=10000)
                
                logger.info("✅ Successfully navigated via menu")
                self.results.append({"test": "navigation", "status": "success", "message": "Menu navigation successful"})
                
            except Exception as menu_error:
                logger.error(f"❌ Navigation failed: {str(menu_error)}")
                self.results.append({"test": "navigation", "status": "failed", "error": str(menu_error)})
                raise
    
    async def test_skill_assessment_form(self, page: Page):
        """Test the skill assessment form functionality"""
        logger.info("📝 Testing Skill Assessment Form")
        
        try:
            # Check if we're on the Assess Skills tab
            assess_tab = page.locator('text="Assess Skills", [data-value="assess"]')
            if await assess_tab.count() > 0:
                await assess_tab.click()
                await page.wait_for_timeout(1000)
            
            # Fill out skill assessment form
            skill_name_input = page.locator('input[placeholder*="JavaScript"], input[placeholder*="skill"], input[id*="skill-name"]').first
            if await skill_name_input.count() > 0:
                await skill_name_input.fill("JavaScript")
                await page.wait_for_timeout(500)
            
            # Interact with rating sliders
            rating_sliders = page.locator('[role="slider"], .slider, input[type="range"]')
            if await rating_sliders.count() > 0:
                # Set self-rating
                await rating_sliders.first.click()
                await page.wait_for_timeout(500)
                
                # Set confidence level if there's a second slider
                if await rating_sliders.count() > 1:
                    await rating_sliders.nth(1).click()
                    await page.wait_for_timeout(500)
            
            # Fill additional fields if present
            years_input = page.locator('input[type="number"], input[placeholder*="years"]')
            if await years_input.count() > 0:
                await years_input.first.fill("3")
            
            notes_textarea = page.locator('textarea, input[placeholder*="notes"]')
            if await notes_textarea.count() > 0:
                await notes_textarea.first.fill("Test assessment for JavaScript skills")
            
            # Submit the assessment
            submit_button = page.locator('button:has-text("Submit"), button[type="submit"]')
            if await submit_button.count() > 0:
                await submit_button.click()
                await page.wait_for_timeout(2000)
            
            logger.info("✅ Skill assessment form completed")
            self.results.append({"test": "skill_assessment", "status": "success", "message": "Form submission successful"})
            
        except Exception as e:
            logger.error(f"❌ Skill assessment failed: {str(e)}")
            self.results.append({"test": "skill_assessment", "status": "failed", "error": str(e)})
            # Don't raise - continue with other tests
    
    async def test_gap_analysis_workflow(self, page: Page):
        """Test the gap analysis workflow"""
        logger.info("🔍 Testing Gap Analysis Workflow")
        
        try:
            # Navigate to Analyze Gaps tab
            analyze_tab = page.locator('text="Analyze Gaps", [data-value="analyze"]')
            if await analyze_tab.count() > 0:
                await analyze_tab.click()
                await page.wait_for_timeout(1000)
            
            # Fill career path
            career_path_input = page.locator('input[placeholder*="Full Stack"], input[placeholder*="career"]')
            if await career_path_input.count() > 0:
                await career_path_input.fill("Full Stack Developer")
                await page.wait_for_timeout(500)
            
            # Select target level
            target_level_select = page.locator('select, [role="combobox"]')
            if await target_level_select.count() > 0:
                await target_level_select.first.select_option("ADVANCED")
                await page.wait_for_timeout(500)
            
            # Set hours per week
            hours_input = page.locator('input[type="number"]:not([placeholder*="years"])')
            if await hours_input.count() > 0:
                await hours_input.fill("15")
                await page.wait_for_timeout(500)
            
            # Start analysis
            analyze_button = page.locator('button:has-text("Analyze"), button:has-text("Start Analysis")')
            if await analyze_button.count() > 0:
                await analyze_button.click()
                
                # Wait for analysis to complete (with timeout)
                try:
                    await page.wait_for_selector('text="Analysis completed", .analysis-results, [data-testid="results"]', timeout=30000)
                    logger.info("✅ Gap analysis completed")
                    self.results.append({"test": "gap_analysis", "status": "success", "message": "Analysis completed"})
                except:
                    logger.info("⏳ Analysis taking longer than expected, continuing...")
                    self.results.append({"test": "gap_analysis", "status": "partial", "message": "Analysis initiated"})
            
        except Exception as e:
            logger.error(f"❌ Gap analysis failed: {str(e)}")
            self.results.append({"test": "gap_analysis", "status": "failed", "error": str(e)})
    
    async def test_results_display(self, page: Page):
        """Test the results display functionality"""
        logger.info("📊 Testing Results Display")
        
        try:
            # Navigate to Results tab
            results_tab = page.locator('text="View Results", [data-value="results"]')
            if await results_tab.count() > 0:
                await results_tab.click()
                await page.wait_for_timeout(2000)
            
            # Check for results components
            components_to_check = [
                'text="Career Readiness"',
                'text="Skill Gaps"',
                'text="Learning Plan"',
                '.progress, [role="progressbar"]',
                '.chart, .graph, .visualization'
            ]
            
            found_components = 0
            for component in components_to_check:
                if await page.locator(component).count() > 0:
                    found_components += 1
            
            if found_components > 0:
                logger.info(f"✅ Results display working - found {found_components} components")
                self.results.append({"test": "results_display", "status": "success", "message": f"Found {found_components} result components"})
            else:
                logger.info("⚠️ No results found - may need to complete analysis first")
                self.results.append({"test": "results_display", "status": "partial", "message": "No results found"})
            
        except Exception as e:
            logger.error(f"❌ Results display test failed: {str(e)}")
            self.results.append({"test": "results_display", "status": "failed", "error": str(e)})
    
    def generate_report(self):
        """Generate test report"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        report = {
            "test_suite": "Skill Gap Analyzer",
            "timestamp": timestamp,
            "total_tests": len(self.results),
            "passed": len([r for r in self.results if r["status"] == "success"]),
            "failed": len([r for r in self.results if r["status"] == "failed"]),
            "partial": len([r for r in self.results if r["status"] == "partial"]),
            "results": self.results
        }
        
        # Save report
        report_file = f"skill_gap_analyzer_test_report_{timestamp}.json"
        with open(report_file, 'w') as f:
            json.dump(report, f, indent=2)
        
        # Print summary
        print("\n" + "="*60)
        print("🧪 SKILL GAP ANALYZER TEST REPORT")
        print("="*60)
        print(f"📅 Timestamp: {timestamp}")
        print(f"📊 Total Tests: {report['total_tests']}")
        print(f"✅ Passed: {report['passed']}")
        print(f"❌ Failed: {report['failed']}")
        print(f"⚠️  Partial: {report['partial']}")
        print(f"📄 Report saved: {report_file}")
        print("="*60)
        
        for result in self.results:
            status_icon = "✅" if result["status"] == "success" else "❌" if result["status"] == "failed" else "⚠️"
            print(f"{status_icon} {result['test']}: {result.get('message', result.get('error', 'No details'))}")
        
        return report

async def main():
    """Main test execution"""
    tester = SkillGapAnalyzerTester()
    
    try:
        await tester.run_comprehensive_test()
    except Exception as e:
        logger.error(f"Test execution failed: {str(e)}")
    finally:
        tester.generate_report()

if __name__ == "__main__":
    asyncio.run(main())
